import { makeAutoObservable, reaction } from "mobx";
import { Network } from "@/types/network.ts";
import { networks } from "@/config/network.ts";
import Token from "@/types/token";
import { rootStore } from "@/store/index.ts";

export class DepositStore {
  fromNetwork: Network;
  destNetwork: Network;

  receiptAddress: string | undefined;

  selectedToken: Token | undefined;

  inputAmount: string = "";

  isReceiptAddressValid: boolean = true;

  constructor() {
    this.fromNetwork = networks[0];
    this.destNetwork = this.fromNetwork.destNetworks[0];

    makeAutoObservable(this);

    this.watch();
  }

  private watch() {
    reaction(
      () => ({
        fromNetwork: this.fromNetwork,
        destNetwork: this.destNetwork,
      }),
      async ({ fromNetwork, destNetwork }) => {
        if (fromNetwork && destNetwork) {
          await rootStore.tokenStore.getTokenList(
            fromNetwork.chainId,
            destNetwork.chainId,
          );
          if (rootStore.tokenStore.tokenList.length > 0) {
            this.selectedToken = rootStore.tokenStore.tokenList[0];
          }
        }
      },
      {
        fireImmediately: true,
      },
    );
  }

  setFromNetwork(network: Network) {
    this.fromNetwork = network;
    this.destNetwork = network.destNetworks[0];
  }

  setDestNetwork(network: Network) {
    this.destNetwork = network;
  }

  swapNetworks() {
    const temp = this.fromNetwork;
    this.fromNetwork = this.destNetwork;
    this.destNetwork = temp;
  }

  setReceiptAddress(address: string | undefined) {
    this.receiptAddress = address;
  }

  setSelectedToken(token: Token) {
    this.selectedToken = token;
  }

  setInputAmount(amount: string) {
    this.inputAmount = amount;
  }

  setReceiptAddressValid(isValid: boolean) {
    this.isReceiptAddressValid = isValid;
  }
}
