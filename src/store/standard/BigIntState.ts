import { makeAutoObservable } from "mobx";
import { formatBalance } from "@/lib/utils/util";
import { formatUnits, parseUnits } from "viem";

export class BigIntState {
  value: bigint = 0n;
  decimal = 18;
  loading: boolean = false;
  private _amount: string = "0";

  constructor(args: Partial<BigIntState>) {
    Object.assign(this, args);
    makeAutoObservable(this);
    // 初始化时计算 amount
    this.updateAmount();
  }

  /**
   * 获取格式化的 amount
   * 当 loading 为 true 时返回 "..."
   */
  get amount(): string {
    if (this.loading) {
      return "...";
    }
    return this._amount;
  }

  /**
   * 设置 amount 值
   * @param amount - 格式化后的数值字符串
   */
  setAmount(amount: string) {
    if (this.loading) return;

    // 移除千分位分隔符
    const cleanAmount = amount.replace(/,/g, "");
    this._amount = formatBalance(cleanAmount, {
      useThousandsSeparator: true,
    });

    // 同时更新 value
    try {
      this.value = parseUnits(cleanAmount, this.decimal);
    } catch (error) {
      console.warn("Invalid amount format:", amount);
    }
  }

  /**
   * 设置 value 值并自动更新 amount
   * @param value - bigint 类型的值
   */
  setValue(value: bigint) {
    this.value = value;
    this.updateAmount();
  }

  /**
   * 设置 decimal 位数并重新计算 amount
   * @param decimal - 小数位数
   */
  setDecimal(decimal: number) {
    this.decimal = decimal;
    this.updateAmount();
  }

  /**
   * 设置 loading 状态
   * @param loading - 是否正在加载
   */
  setLoading(loading: boolean) {
    this.loading = loading;
    // 当 loading 状态改变时，如果变为 false，需要重新计算 amount
    if (!loading) {
      this.updateAmount();
    }
  }

  /**
   * 使用 wagmi 的 formatUnits 更新 amount
   * @private
   */
  private updateAmount() {
    if (this.loading) return;

    try {
      // 使用 wagmi 的 formatUnits 处理 actualValue
      const actualValue = formatUnits(this.value, this.decimal);

      // 使用 formatBalance 进行格式化，自动处理千分位
      this._amount = formatBalance(actualValue, {
        useThousandsSeparator: true,
      });
    } catch (error) {
      console.warn("Error updating amount:", error);
      this._amount = "0";
    }
  }

  /**
   * 获取原始的数值字符串（不带千分位格式化）
   */
  get rawAmount(): string {
    if (this.loading) {
      return "...";
    }

    try {
      return formatUnits(this.value, this.decimal);
    } catch (error) {
      return "0";
    }
  }

  /**
   * 获取带千分位格式化的 amount
   */
  get formattedAmount(): string {
    return this.amount;
  }
}
