export const ERC20_ABI = [
  // name() function
  {
    constant: true,
    inputs: [],
    name: "name",
    outputs: [{ name: "", type: "string" }],
    payable: false,
    stateMutability: "view",
    type: "function",
  },
  // approve(address,uint256) function
  {
    constant: false,
    inputs: [
      { name: "_spender", type: "address" },
      { name: "_value", type: "uint256" }
    ],
    name: "approve",
    outputs: [{ name: "", type: "bool" }],
    payable: false,
    stateMutability: "nonpayable",
    type: "function",
  },
  // totalSupply() function
  {
    constant: true,
    inputs: [],
    name: "totalSupply",
    outputs: [{ name: "", type: "uint256" }],
    payable: false,
    stateMutability: "view",
    type: "function",
  },
  // transferFrom(address,address,uint256) function
  {
    constant: false,
    inputs: [
      { name: "_from", type: "address" },
      { name: "_to", type: "address" },
      { name: "_value", type: "uint256" }
    ],
    name: "transfer<PERSON>rom",
    outputs: [{ name: "", type: "bool" }],
    payable: false,
    stateMutability: "nonpayable",
    type: "function",
  },
  // decimals() function
  {
    constant: true,
    inputs: [],
    name: "decimals",
    outputs: [{ name: "", type: "uint8" }],
    payable: false,
    stateMutability: "view",
    type: "function",
  },
  // balanceOf(address) function
  {
    constant: true,
    inputs: [{ name: "_owner", type: "address" }],
    name: "balanceOf",
    outputs: [{ name: "balance", type: "uint256" }],
    payable: false,
    stateMutability: "view",
    type: "function",
  },
  // symbol() function
  {
    constant: true,
    inputs: [],
    name: "symbol",
    outputs: [{ name: "", type: "string" }],
    payable: false,
    stateMutability: "view",
    type: "function",
  },
  // transfer(address,uint256) function
  {
    constant: false,
    inputs: [
      { name: "_to", type: "address" },
      { name: "_value", type: "uint256" }
    ],
    name: "transfer",
    outputs: [{ name: "", type: "bool" }],
    payable: false,
    stateMutability: "nonpayable",
    type: "function",
  },
  // allowance(address,address) function
  {
    constant: true,
    inputs: [
      { name: "_owner", type: "address" },
      { name: "_spender", type: "address" }
    ],
    name: "allowance",
    outputs: [{ name: "", type: "uint256" }],
    payable: false,
    stateMutability: "view",
    type: "function",
  },
  // fallback function
  {
    payable: true,
    stateMutability: "payable",
    type: "fallback",
  },
  // Approval event
  {
    anonymous: false,
    inputs: [
      { indexed: true, name: "owner", type: "address" },
      { indexed: true, name: "spender", type: "address" },
      { indexed: false, name: "value", type: "uint256" }
    ],
    name: "Approval",
    type: "event",
  },
  // Transfer event
  {
    anonymous: false,
    inputs: [
      { indexed: true, name: "from", type: "address" },
      { indexed: true, name: "to", type: "address" },
      { indexed: false, name: "value", type: "uint256" }
    ],
    name: "Transfer",
    type: "event",
  },
] as const;

/**
 * ERC20 ABI 类 - 提供类型安全的 ABI 访问和工具方法
 */
export class ERC20ABI {
  /**
   * 完整的 ERC20 ABI
   */
  static readonly FULL = ERC20_ABI;

  /**
   * 只读函数 ABI（view 函数）
   */
  static readonly READ_ONLY = [
    // name() function
    {
      constant: true,
      inputs: [],
      name: "name",
      outputs: [{ name: "", type: "string" }],
      payable: false,
      stateMutability: "view",
      type: "function",
    },
    // totalSupply() function
    {
      constant: true,
      inputs: [],
      name: "totalSupply",
      outputs: [{ name: "", type: "uint256" }],
      payable: false,
      stateMutability: "view",
      type: "function",
    },
    // decimals() function
    {
      constant: true,
      inputs: [],
      name: "decimals",
      outputs: [{ name: "", type: "uint8" }],
      payable: false,
      stateMutability: "view",
      type: "function",
    },
    // balanceOf(address) function
    {
      constant: true,
      inputs: [{ name: "_owner", type: "address" }],
      name: "balanceOf",
      outputs: [{ name: "balance", type: "uint256" }],
      payable: false,
      stateMutability: "view",
      type: "function",
    },
    // symbol() function
    {
      constant: true,
      inputs: [],
      name: "symbol",
      outputs: [{ name: "", type: "string" }],
      payable: false,
      stateMutability: "view",
      type: "function",
    },
    // allowance(address,address) function
    {
      constant: true,
      inputs: [
        { name: "_owner", type: "address" },
        { name: "_spender", type: "address" }
      ],
      name: "allowance",
      outputs: [{ name: "", type: "uint256" }],
      payable: false,
      stateMutability: "view",
      type: "function",
    },
  ] as const;

  /**
   * 写入函数 ABI（非 view 函数）
   */
  static readonly WRITE_ONLY = [
    // approve(address,uint256) function
    {
      constant: false,
      inputs: [
        { name: "_spender", type: "address" },
        { name: "_value", type: "uint256" }
      ],
      name: "approve",
      outputs: [{ name: "", type: "bool" }],
      payable: false,
      stateMutability: "nonpayable",
      type: "function",
    },
    // transferFrom(address,address,uint256) function
    {
      constant: false,
      inputs: [
        { name: "_from", type: "address" },
        { name: "_to", type: "address" },
        { name: "_value", type: "uint256" }
      ],
      name: "transferFrom",
      outputs: [{ name: "", type: "bool" }],
      payable: false,
      stateMutability: "nonpayable",
      type: "function",
    },
    // transfer(address,uint256) function
    {
      constant: false,
      inputs: [
        { name: "_to", type: "address" },
        { name: "_value", type: "uint256" }
      ],
      name: "transfer",
      outputs: [{ name: "", type: "bool" }],
      payable: false,
      stateMutability: "nonpayable",
      type: "function",
    },
  ] as const;

  /**
   * 事件 ABI
   */
  static readonly EVENTS = [
    // Approval event
    {
      anonymous: false,
      inputs: [
        { indexed: true, name: "owner", type: "address" },
        { indexed: true, name: "spender", type: "address" },
        { indexed: false, name: "value", type: "uint256" }
      ],
      name: "Approval",
      type: "event",
    },
    // Transfer event
    {
      anonymous: false,
      inputs: [
        { indexed: true, name: "from", type: "address" },
        { indexed: true, name: "to", type: "address" },
        { indexed: false, name: "value", type: "uint256" }
      ],
      name: "Transfer",
      type: "event",
    },
  ] as const;

  /**
   * 获取特定函数的 ABI
   */
  static getFunction<T extends string>(functionName: T) {
    const func = ERC20_ABI.find(
      item => item.type === 'function' && item.name === functionName
    );
    if (!func) {
      throw new Error(`Function ${functionName} not found in ERC20 ABI`);
    }
    return [func] as const;
  }

  /**
   * 获取特定事件的 ABI
   */
  static getEvent<T extends string>(eventName: T) {
    const event = ERC20_ABI.find(
      item => item.type === 'event' && item.name === eventName
    );
    if (!event) {
      throw new Error(`Event ${eventName} not found in ERC20 ABI`);
    }
    return [event] as const;
  }

  /**
   * 常用函数的便捷访问
   */
  static readonly Functions = {
    balanceOf: ERC20ABI.getFunction("balanceOf"),
    transfer: ERC20ABI.getFunction("transfer"),
    transferFrom: ERC20ABI.getFunction("transferFrom"),
    approve: ERC20ABI.getFunction("approve"),
    allowance: ERC20ABI.getFunction("allowance"),
    name: ERC20ABI.getFunction("name"),
    symbol: ERC20ABI.getFunction("symbol"),
    decimals: ERC20ABI.getFunction("decimals"),
    totalSupply: ERC20ABI.getFunction("totalSupply"),
  } as const;

  /**
   * 常用事件的便捷访问
   */
  static readonly Events = {
    Transfer: ERC20ABI.getEvent("Transfer"),
    Approval: ERC20ABI.getEvent("Approval"),
  } as const;
}

/**
 * 向后兼容的导出
 */
export const ERC20_BALANCE_OF_ABI = ERC20ABI.Functions.balanceOf;
export const ERC20_TRANSFER_ABI = ERC20ABI.Functions.transfer;
export const ERC20_APPROVE_ABI = ERC20ABI.Functions.approve;
export const ERC20_ALLOWANCE_ABI = ERC20ABI.Functions.allowance;
export const ERC20_NAME_ABI = ERC20ABI.Functions.name;
export const ERC20_SYMBOL_ABI = ERC20ABI.Functions.symbol;
export const ERC20_DECIMALS_ABI = ERC20ABI.Functions.decimals;
export const ERC20_TOTAL_SUPPLY_ABI = ERC20ABI.Functions.totalSupply;

/**
 * 常用的 ERC20 函数 ABI 集合（向后兼容）
 */
export const ERC20_ABIS = {
  balanceOf: ERC20_BALANCE_OF_ABI,
  transfer: ERC20_TRANSFER_ABI,
  approve: ERC20_APPROVE_ABI,
  allowance: ERC20_ALLOWANCE_ABI,
  name: ERC20_NAME_ABI,
  symbol: ERC20_SYMBOL_ABI,
  decimals: ERC20_DECIMALS_ABI,
  totalSupply: ERC20_TOTAL_SUPPLY_ABI,
} as const;
