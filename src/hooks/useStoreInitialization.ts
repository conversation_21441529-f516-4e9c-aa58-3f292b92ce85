import { useEffect, useState } from "react";
import { useStore } from "@/store";

interface InitializationState {
  isInitialized: boolean;
  isLoading: boolean;
  error: string | null;
  retry: () => void;
}

/**
 * 自定义 Hook 用于管理 Store 初始化
 * @param autoInit 是否自动初始化，默认为 true
 * @param dependencies 依赖数组，当依赖变化时重新初始化
 */
export function useStoreInitialization(
  autoInit: boolean = true,
  dependencies: any[] = []
): InitializationState {
  const store = useStore();
  const [isInitialized, setIsInitialized] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const initialize = async () => {
    if (isLoading) return; // 防止重复初始化

    setIsLoading(true);
    setError(null);

    try {
      await store.initialize();
      setIsInitialized(true);
    } catch (err) {
      console.error("Store initialization failed:", err);
      setError(err instanceof Error ? err.message : "初始化失败");
      setIsInitialized(false);
    } finally {
      setIsLoading(false);
    }
  };

  const retry = () => {
    setError(null);
    setIsInitialized(false);
    initialize();
  };

  useEffect(() => {
    if (autoInit) {
      initialize();
    }
  }, [autoInit, ...dependencies]);

  return {
    isInitialized,
    isLoading,
    error,
    retry,
  };
}

/**
 * 专门用于语言初始化的 Hook
 */
export function useLangInitialization(): InitializationState {
  const store = useStore();
  const [isInitialized, setIsInitialized] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const initialize = async () => {
    if (isLoading) return;

    setIsLoading(true);
    setError(null);

    try {
      await store.lang.init();
      setIsInitialized(true);
    } catch (err) {
      console.error("Language initialization failed:", err);
      setError(err instanceof Error ? err.message : "语言初始化失败");
      setIsInitialized(false);
    } finally {
      setIsLoading(false);
    }
  };

  const retry = () => {
    setError(null);
    setIsInitialized(false);
    initialize();
  };

  useEffect(() => {
    initialize();
  }, []);

  return {
    isInitialized,
    isLoading,
    error,
    retry,
  };
}

/**
 * 用于代币数据初始化的 Hook
 */
export function useTokenInitialization(
  fromChainId?: number,
  destChainId?: number
): InitializationState {
  const store = useStore();
  const [isInitialized, setIsInitialized] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const initialize = async () => {
    if (!fromChainId || !destChainId || isLoading) return;

    setIsLoading(true);
    setError(null);

    try {
      await store.tokenStore.getTokenList(fromChainId, destChainId);
      setIsInitialized(true);
    } catch (err) {
      console.error("Token initialization failed:", err);
      setError(err instanceof Error ? err.message : "代币数据加载失败");
      setIsInitialized(false);
    } finally {
      setIsLoading(false);
    }
  };

  const retry = () => {
    setError(null);
    setIsInitialized(false);
    initialize();
  };

  useEffect(() => {
    if (fromChainId && destChainId) {
      initialize();
    }
  }, [fromChainId, destChainId]);

  return {
    isInitialized,
    isLoading,
    error,
    retry,
  };
}
