import { BigIntState } from "@/store/standard/BigIntState.ts";
import { makeAutoObservable } from "mobx";

export default class Token {
  id?: number;
  chainid?: number;
  dest_chainid?: number;
  address?: string;
  ctoken_address?: string;
  dest_address?: string;
  name?: string;
  decimals?: number;
  symbol?: string;
  logouri?: string;
  cashier?: string;
  quick_swap_from?: string;
  quick_swap?: string;
  token_only_dest_wrapper?: string;
  need_unwrapper?: boolean;
  is_popular?: boolean;
  is_depintoken?: boolean;
  is_wrapped?: boolean;
  createat?: string;
  updateat?: string;
  router_address?: string;
  usdc_dest_wrapper?: string;
  ctoken?: string;

  balance: BigIntState = new BigIntState({});

  constructor(args: Partial<BigIntState>) {
    Object.assign(this, args);
    makeAutoObservable(this);
  }

  setBalance(val: bigint) {
    this.balance.setValue(val);
  }
}
