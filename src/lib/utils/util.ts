import clsx, { type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";
import { isAddress } from "viem";

export const ellipseStr = (text: string, length = 10) => {
  if (text.length <= length) {
    return text;
  }
  return text.slice(0, length) + "..." + text.slice(-length - 1);
};

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * 格式化余额显示
 * @param value - 要格式化的数值（字符串或数字）
 * @param options - 格式化选项
 * @returns 格式化后的字符串
 *
 * 规则：
 * - 整数部分使用千分位格式化
 * - 当值 < 1 时，小数部分保留四位有效数字
 * - 当值 >= 1 时，小数部分保留4位
 * - 去掉小数末尾的0
 *
 * @example
 * formatBalance("1234.56789") // "1,234.5679"
 * formatBalance("0.00012345") // "0.0001235"
 * formatBalance("0.1234567") // "0.1235"
 * formatBalance("1000") // "1,000"
 * formatBalance("1000.0000") // "1,000"
 */
export function formatBalance(
  value: string | number,
  options: {
    /** 当值小于1时的有效数字位数，默认4位 */
    significantDigitsForSmall?: number;
    /** 当值大于等于1时的小数位数，默认4位 */
    decimalPlacesForLarge?: number;
    /** 是否显示千分位分隔符，默认true */
    useThousandsSeparator?: boolean;
  } = {},
): string {
  const {
    significantDigitsForSmall = 4,
    decimalPlacesForLarge = 4,
    useThousandsSeparator = true,
  } = options;

  if (value === null || value === undefined || value === "") {
    return "0";
  }

  const numValue = typeof value === "string" ? parseFloat(value) : value;

  if (isNaN(numValue) || !isFinite(numValue)) {
    return "0";
  }

  if (numValue === 0) {
    return "0";
  }

  const isNegative = numValue < 0;
  const absValue = Math.abs(numValue);

  let formattedValue: string;

  if (absValue < 1) {
    if (absValue < 1e-6) {
      const exponent = Math.floor(Math.log10(absValue));
      const mantissa = absValue / Math.pow(10, exponent);
      const roundedMantissa = parseFloat(
        mantissa.toPrecision(significantDigitsForSmall),
      );
      const result = roundedMantissa * Math.pow(10, exponent);
      formattedValue = result.toFixed(
        -exponent + significantDigitsForSmall - 1,
      );
    } else {
      formattedValue = absValue.toPrecision(significantDigitsForSmall);
      const num = parseFloat(formattedValue);
      formattedValue = num.toString();
    }
  } else {
    formattedValue = absValue.toFixed(decimalPlacesForLarge);
  }

  formattedValue = formattedValue.replace(/\.?0+$/, "");

  if (useThousandsSeparator) {
    const parts = formattedValue.split(".");
    const integerPart = parts[0];
    const decimalPart = parts[1];

    const formattedInteger = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ",");

    formattedValue = decimalPart
      ? `${formattedInteger}.${decimalPart}`
      : formattedInteger;
  }

  return isNegative ? `-${formattedValue}` : formattedValue;
}

/**
 * 验证是否是合法的 EVM 地址
 * @param address - 要验证的地址字符串
 * @returns 如果是合法的 EVM 地址返回 true，否则返回 false
 *
 * @example
 * isValidEvmAddress("0x742d35Cc6634C0532925a3b8D4C9db96C4b4Df8") // true
 * isValidEvmAddress("******************************************") // true (42 characters)
 * isValidEvmAddress("0x742d35Cc6634C0532925a3b8D4C9db96C4b4Df") // false (too short)
 * isValidEvmAddress("742d35Cc6634C0532925a3b8D4C9db96C4b4Df8a") // false (no 0x prefix)
 * isValidEvmAddress("0xGGGd35Cc6634C0532925a3b8D4C9db96C4b4Df8a") // false (invalid hex)
 * isValidEvmAddress("") // false
 * isValidEvmAddress(null) // false
 */
export function isValidEvmAddress(address: string | null | undefined): boolean {
  if (!address || typeof address !== "string") {
    return false;
  }

  try {
    return isAddress(address);
  } catch (error) {
    return false;
  }
}

/**
 * 获取地址的简短显示格式
 * @param address - EVM 地址
 * @param startLength - 开头保留的字符数，默认 6
 * @param endLength - 结尾保留的字符数，默认 4
 * @returns 简短格式的地址，如果地址无效返回原字符串
 *
 * @example
 * getShortEvmAddress("******************************************") // "0x742d...Df8a"
 * getShortEvmAddress("******************************************", 8, 6) // "0x742d35...4Df8a"
 * getShortEvmAddress("invalid-address") // "invalid-address"
 */
export function getShortEvmAddress(
  address: string | null | undefined,
  startLength: number = 6,
  endLength: number = 4,
): string {
  if (!address) {
    return "";
  }

  if (!isValidEvmAddress(address)) {
    return address;
  }

  if (address.length <= startLength + endLength + 3) {
    return address;
  }

  return `${address.slice(0, startLength)}...${address.slice(-endLength)}`;
}
