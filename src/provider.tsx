import type { NavigateOptions } from "react-router-dom";
import { useHref, useNavigate } from "react-router-dom";

import { HeroUIProvider } from "@heroui/react";
import {
  AppKitNetwork,
  solana,
  mainnet,
  bsc,
  polygon,
  iotex,
} from "@reown/appkit/networks";
import { createAppKit } from "@reown/appkit/react";
import { WagmiAdapter } from "@reown/appkit-adapter-wagmi";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { WagmiProvider } from "wagmi";
import { StoresProvider } from "./store";

declare module "@react-types/shared" {
  interface RouterConfig {
    routerOptions: NavigateOptions;
  }
}

// Create QueryClient instance
const queryClient = new QueryClient();

// Create Wagmi adapter with all supported networks
export const wagmiAdapter = new WagmiAdapter({
  networks: [mainnet, bsc, polygon, iotex, solana],
  projectId: import.meta.env.VITE_PROJECT_ID || "",
});

function init() {
  const projectId = import.meta.env.VITE_PROJECT_ID;
  if (!projectId) {
    throw new Error("VITE_PROJECT_ID is not set");
  }

  const metadata = {
    name: "ioTube",
    description: "ioTube",
    url: "https://tg-min-app.onrender.com",
    icons: ["https://tg-min-app.onrender.com/image/logo.png"],
  };

  const networks: [AppKitNetwork, ...AppKitNetwork[]] = [
    mainnet,
    bsc,
    polygon,
    iotex,
    solana,
  ];

  createAppKit({
    adapters: [wagmiAdapter],
    networks,
    metadata,
    projectId,
    featuredWalletIds: [
      "c57ca95b47569778a828d19178114f4db188b89b763c899ba0be274e97267d96",
      "1a5f2435e8e31c4034f1d142e85d9f7d3be2a09ddf710e5ef1ad4e36c719d3c0",
      "971e689d0a5be527bac79629b4ee9b925e82208e5168b733496a09c0faed0709",
    ],
    features: {
      email: false,
      socials: [],
      emailShowWallets: false,
      swaps: false,
    },
  });
}

export function Provider({ children }: { children: React.ReactNode }) {
  const navigate = useNavigate();

  init();

  return (
    <WagmiProvider config={wagmiAdapter.wagmiConfig}>
      <QueryClientProvider client={queryClient}>
        <HeroUIProvider navigate={navigate} useHref={useHref}>
          <StoresProvider>{children}</StoresProvider>
        </HeroUIProvider>
      </QueryClientProvider>
    </WagmiProvider>
  );
}
