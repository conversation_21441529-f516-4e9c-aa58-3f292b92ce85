{"name": "vite-template", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint -c .eslintrc.json ./src/**/**/*.{ts,tsx} --fix", "preview": "vite preview"}, "dependencies": {"@heroui/react": "^2.8.2", "@heroui/use-theme": "^2.1.10", "@ledgerhq/devices": "6.27.1", "@ledgerhq/errors": "6.19.1", "@ledgerhq/hw-app-eth": "6.11.2", "@ledgerhq/hw-transport": "6.11.2", "@ledgerhq/hw-transport-webhid": "6.11.2", "@ledgerhq/hw-transport-webusb": "6.11.2", "@ledgerhq/logs": "6.12.0", "@react-aria/visually-hidden": "3.8.19", "@react-types/shared": "3.27.0", "@reown/appkit": "^1.7.1", "@reown/appkit-adapter-wagmi": "^1.7.16", "@tanstack/react-query": "^5.83.0", "@wagmi/core": "^2.18.1", "axios": "^1.8.0", "bs58": "^6.0.0", "clsx": "2.1.1", "framer-motion": "11.15.0", "lodash": "^4.17.21", "mobx": "^6.13.6", "mobx-react-lite": "^4.1.0", "react": "18.3.1", "react-dom": "18.3.1", "react-hot-toast": "^2.5.2", "react-router-dom": "6.23.0", "tailwind-merge": "^3.3.1", "tailwind-variants": "0.3.0", "tailwindcss": "3.4.16", "uuid": "^11.1.0", "viem": "^2.33.1", "wagmi": "^2.16.0"}, "devDependencies": {"@types/lodash": "^4.17.16", "@types/node": "20.5.7", "@types/react": "18.3.3", "@types/react-dom": "18.3.0", "@typescript-eslint/eslint-plugin": "8.11.0", "@typescript-eslint/parser": "8.11.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "10.4.19", "eslint": "^8.57.0", "eslint-config-prettier": "9.1.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jsx-a11y": "^6.4.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "5.2.1", "eslint-plugin-react": "^7.23.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-unused-imports": "4.1.4", "postcss": "8.4.38", "prettier": "3.3.3", "typescript": "5.6.3", "vite": "^5.2.0", "vite-tsconfig-paths": "^4.3.2"}, "resolutions": {"@ledgerhq/hw-transport": "6.11.2"}}