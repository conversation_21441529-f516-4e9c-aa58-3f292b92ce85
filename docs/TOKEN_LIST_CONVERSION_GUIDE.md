# Token 列表转换指南

本指南介绍如何使用增强的 `getTokenList` 方法，该方法现在会自动将 JSON 数据转换为 `Token[]` 类型实例。

## 功能概述

新的 `getTokenList` 方法现在提供：

1. **自动类型转换**: JSON 数据自动转换为 `Token` 类实例
2. **完整的类功能**: 每个 Token 都具有完整的方法和计算属性
3. **余额状态管理**: 自动初始化 `BigIntState` 余额管理
4. **缓存优化**: 智能缓存机制，提高性能
5. **错误处理**: 完善的错误处理和后备机制

## 核心实现

### 1. 类型转换函数

```typescript
private convertToTokenInstances(tokenData: any[]): Token[] {
  return tokenData.map(data => {
    // 创建新的 Token 实例
    const token = new Token({});
    
    // 将数据属性复制到 Token 实例
    Object.assign(token, data);
    
    // 设置初始小数位数
    if (token.decimals !== undefined) {
      token.balance.setDecimal(token.decimals);
    }
    
    return token;
  });
}
```

### 2. 增强的 getTokenList 方法

```typescript
async getTokenList(chainId: number, destChainId: number) {
  // 缓存检查和转换
  if (cachedData && cachedTimestamp) {
    const parsedData = JSON.parse(cachedData);
    this.tokenList = this.convertToTokenInstances(parsedData);
    return;
  }

  // 网络请求和转换
  const tokensData = response.data || [];
  this.tokenList = this.convertToTokenInstances(tokensData);
}
```

## 使用方法

### 1. 基础用法

```typescript
import { useStore } from '@/store';

function MyComponent() {
  const { tokenStore } = useStore();

  const fetchTokens = async () => {
    try {
      await tokenStore.getTokenList(4689, 1); // IoTeX to Ethereum
      
      // tokenStore.tokenList 现在包含完整的 Token 实例
      tokenStore.tokenList.forEach(token => {
        console.log(token.symbol); // 直接访问属性
        console.log(token.balance.amount); // 使用 BigIntState 功能
        token.setBalance(1000n); // 调用实例方法
      });
    } catch (error) {
      console.error('获取失败:', error);
    }
  };

  return <button onClick={fetchTokens}>获取 Token 列表</button>;
}
```

### 2. 在组件中使用

```typescript
import { observer } from 'mobx-react-lite';

const TokenList = observer(() => {
  const { tokenStore } = useStore();

  useEffect(() => {
    tokenStore.getTokenList(1, 4689); // Ethereum to IoTeX
  }, []);

  return (
    <div>
      {tokenStore.tokenList.map(token => (
        <div key={token.id}>
          <h3>{token.symbol} - {token.name}</h3>
          <p>余额: {token.balance.amount}</p>
          <p>精度: {token.decimals}</p>
          <p>热门: {token.is_popular ? '是' : '否'}</p>
        </div>
      ))}
    </div>
  );
});
```

### 3. 结合余额获取

```typescript
const TokenManager = observer(() => {
  const { tokenStore } = useStore();
  const { address } = useAppKitAccount();

  const fetchTokensAndBalances = async () => {
    try {
      // 1. 获取 Token 列表（自动转换为 Token 实例）
      await tokenStore.getTokenList(4689, 1);
      
      // 2. 获取余额（如果钱包已连接）
      if (address && tokenStore.tokenList.length > 0) {
        await tokenStore.batchFetchTokenBalance(tokenStore.tokenList, address);
      }
    } catch (error) {
      console.error('操作失败:', error);
    }
  };

  return (
    <div>
      <button onClick={fetchTokensAndBalances}>
        获取 Token 和余额
      </button>
      
      {tokenStore.tokenList.map(token => (
        <TokenCard key={token.id} token={token} />
      ))}
    </div>
  );
});
```

## Token 实例的优势

### 1. 完整的类功能

```typescript
// 之前：普通对象
const token = { symbol: 'USDT', decimals: 6, balance: null };

// 现在：Token 类实例
const token = new Token(data);
token.setBalance(1000000n); // 方法调用
console.log(token.balance.amount); // "1.00" (自动格式化)
console.log(token.balance.loading); // false
```

### 2. 响应式状态管理

```typescript
// Token 实例具有 MobX 响应式状态
const token = tokenStore.tokenList[0];

// 设置余额会自动触发 UI 更新
token.setBalance(2000000n);

// 设置加载状态
token.balance.setLoading(true);
```

### 3. 类型安全

```typescript
// TypeScript 完全支持
tokenStore.tokenList.forEach((token: Token) => {
  token.setBalance(100n); // ✅ 类型安全
  token.balance.setDecimal(18); // ✅ 方法存在
  token.unknownMethod(); // ❌ 编译错误
});
```

## 缓存机制

### 1. 智能缓存

```typescript
// 缓存原始 JSON 数据
localStorage.setItem(cacheKey, JSON.stringify(tokensData));

// 读取时转换为 Token 实例
const parsedData = JSON.parse(cachedData);
this.tokenList = this.convertToTokenInstances(parsedData);
```

### 2. 缓存失效

- 缓存时间：1小时（3600000ms）
- 自动清理：解析失败时清除缓存
- 后备机制：网络失败时使用缓存

## 错误处理

### 1. 网络错误

```typescript
try {
  const response = await axios.get(url);
  this.tokenList = this.convertToTokenInstances(response.data);
} catch (error) {
  // 使用缓存作为后备
  if (cachedData) {
    const parsedData = JSON.parse(cachedData);
    this.tokenList = this.convertToTokenInstances(parsedData);
  } else {
    this.tokenList = [];
  }
}
```

### 2. 解析错误

```typescript
try {
  const parsedData = JSON.parse(cachedData);
  this.tokenList = this.convertToTokenInstances(parsedData);
} catch (parseError) {
  console.error('解析缓存数据失败:', parseError);
  localStorage.removeItem(cacheKey);
  this.tokenList = [];
}
```

## 性能优化

### 1. 缓存策略

- 按 `chainId` 和 `destChainId` 分别缓存
- 避免重复网络请求
- 智能缓存失效

### 2. 内存管理

- Token 实例复用 BigIntState
- 避免不必要的对象创建
- MobX 自动优化响应式更新

## 最佳实践

### 1. 错误处理

```typescript
const fetchTokens = async () => {
  try {
    await tokenStore.getTokenList(chainId, destChainId);
  } catch (error) {
    // 显示用户友好的错误信息
    showErrorMessage('无法获取 Token 列表，请检查网络连接');
    console.error('详细错误:', error);
  }
};
```

### 2. 加载状态

```typescript
const [loading, setLoading] = useState(false);

const fetchTokens = async () => {
  setLoading(true);
  try {
    await tokenStore.getTokenList(chainId, destChainId);
  } finally {
    setLoading(false);
  }
};
```

### 3. 自动刷新

```typescript
useEffect(() => {
  const fetchData = () => {
    tokenStore.getTokenList(chainId, destChainId);
  };

  fetchData(); // 立即执行
  
  const interval = setInterval(fetchData, 5 * 60 * 1000); // 5分钟刷新
  return () => clearInterval(interval);
}, [chainId, destChainId]);
```

## 注意事项

1. **内存使用**: Token 实例比普通对象占用更多内存
2. **初始化时间**: 类实例创建需要额外时间
3. **缓存大小**: 大量 Token 可能增加缓存大小
4. **网络依赖**: 首次获取仍需网络连接

这个增强的 Token 列表管理系统提供了完整的类型安全、状态管理和性能优化，让 Token 数据的处理变得更加强大和可靠。
