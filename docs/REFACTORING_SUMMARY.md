# Store 重构总结

本文档总结了将账户管理功能从 `TokenStore` 分离到独立的 `AccountStore` 的重构过程。

## 重构概述

### 目标
- 实现单一职责原则
- 提高代码可维护性
- 增强代码复用性
- 改善测试能力

### 重构范围
- 创建新的 `AccountStore` 类
- 重构 `TokenStore` 类
- 更新 `RootStore` 依赖管理
- 创建示例和文档

## 文件变更

### 新增文件

1. **`src/store/accountStore.ts`** - 新的账户管理Store
   - 钱包连接状态管理
   - 地址获取和验证
   - 链ID管理
   - 连接器信息管理

2. **`src/examples/SeparatedStoresExample.tsx`** - 分离架构示例
3. **`docs/STORE_ARCHITECTURE_GUIDE.md`** - 架构指南
4. **`docs/REFACTORING_SUMMARY.md`** - 重构总结

### 修改文件

1. **`src/store/index.ts`** - RootStore 更新
   - 添加 AccountStore 实例管理
   - 设置 TokenStore 依赖注入

2. **`src/store/tokenStore.ts`** - TokenStore 重构
   - 移除钱包相关方法
   - 添加 AccountStore 依赖
   - 委托钱包状态检查

3. **示例文件更新**
   - `src/examples/SimpleWalletUsage.tsx`
   - `src/examples/WalletIntegrationExample.tsx`

## 架构变化

### 重构前
```typescript
class TokenStore {
  // Token 管理
  tokenList: Token[]
  getTokenList()
  batchFetchTokenBalance()
  
  // 钱包管理 (混合职责)
  get walletInfo()
  get isWalletConnected()
  get walletAddress()
  getWalletStatus()
}
```

### 重构后
```typescript
class AccountStore {
  // 专门负责账户管理
  get walletInfo()
  get isWalletConnected()
  get walletAddress()
  get currentChainId()
  getWalletStatus()
  ensureWalletConnected()
  // ... 更多账户相关方法
}

class TokenStore {
  // 专门负责Token管理
  tokenList: Token[]
  private accountStore: AccountStore
  
  getTokenList()
  batchFetchTokenBalance()
  
  // 委托给 AccountStore
  get isWalletConnected()
  get walletAddress()
}
```

## 核心特性

### 1. AccountStore 功能

```typescript
export class AccountStore {
  // 核心状态
  get walletInfo(): WalletInfo
  get isWalletConnected(): boolean
  get walletAddress(): string | undefined
  get currentChainId(): number | undefined

  // 状态查询
  getWalletStatus(): WalletStatus
  getConnectionSummary(): object
  
  // 工具方法
  ensureWalletConnected(): string
  isOnChain(chainId: number): boolean
  getShortAddress(address?: string): string | undefined
  isCurrentWalletAddress(address: string): boolean
  
  // 高级功能
  waitForConnection(timeout?: number): Promise<string>
  isValidAddress(address: string): boolean
  getWalletTypeDescription(): string
  supportsFeature(feature: string): boolean
}
```

### 2. 依赖注入

```typescript
// RootStore 中的配置
get tokenStore(): TokenStore {
  if (!this._tokenStore) {
    this._tokenStore = new TokenStore();
    this._tokenStore.setAccountStore(this.accountStore);
  }
  return this._tokenStore;
}

// TokenStore 中的使用
private getAccountStore(): AccountStore {
  if (!this.accountStore) {
    throw new Error('AccountStore not initialized');
  }
  return this.accountStore;
}
```

### 3. 向后兼容

```typescript
// TokenStore 保持了关键的接口
get isWalletConnected(): boolean {
  return this.accountStore?.isWalletConnected ?? false;
}

get walletAddress(): string | undefined {
  return this.accountStore?.walletAddress;
}

// 余额获取方法保持不变
async fetchCurrentWalletBalances() {
  this.getAccountStore().ensureWalletConnected();
  // ...
}
```

## 使用方式

### 基础用法

```typescript
const { accountStore, tokenStore } = useStore();

// 使用 AccountStore 检查钱包状态
if (accountStore.isWalletConnected) {
  console.log('地址:', accountStore.walletAddress);
  console.log('链ID:', accountStore.currentChainId);
}

// 使用 TokenStore 管理Token
await tokenStore.getTokenList(4689, 1);
await tokenStore.fetchCurrentWalletBalances();
```

### 响应式组件

```typescript
const WalletComponent = observer(() => {
  const { accountStore, tokenStore } = useStore();

  useEffect(() => {
    if (accountStore.isWalletConnected && tokenStore.tokenList.length > 0) {
      tokenStore.fetchCurrentWalletBalances();
    }
  }, [accountStore.isWalletConnected, tokenStore.tokenList.length]);

  return (
    <div>
      <p>钱包: {accountStore.getWalletStatus().addressShort}</p>
      <p>Token: {tokenStore.tokenList.length}</p>
    </div>
  );
});
```

## 重构优势

### 1. 单一职责原则
- **AccountStore**: 只负责账户和钱包管理
- **TokenStore**: 只负责Token和余额管理

### 2. 代码复用
- AccountStore 可以被其他Store复用
- 避免重复的钱包管理代码

### 3. 易于测试
```typescript
// 可以独立测试 AccountStore
const accountStore = new AccountStore();
expect(accountStore.isWalletConnected).toBe(false);

// 可以模拟 AccountStore 来测试 TokenStore
const mockAccountStore = {
  ensureWalletConnected: () => '0x123...',
  isWalletConnected: true,
};
const tokenStore = new TokenStore();
tokenStore.setAccountStore(mockAccountStore);
```

### 4. 易于扩展
- 新增钱包功能只需修改 AccountStore
- 新增Token功能只需修改 TokenStore
- 互不影响

### 5. 更好的错误处理
```typescript
try {
  accountStore.ensureWalletConnected();
  await tokenStore.fetchCurrentWalletBalances();
} catch (error) {
  if (error.message.includes('钱包未连接')) {
    // 处理钱包连接错误
  }
}
```

## 迁移指南

### 1. 更新组件导入

```typescript
// 之前
const { tokenStore } = useStore();
const isConnected = tokenStore.isWalletConnected;

// 现在
const { accountStore, tokenStore } = useStore();
const isConnected = accountStore.isWalletConnected;
```

### 2. 更新钱包状态检查

```typescript
// 之前
if (tokenStore.isWalletConnected) {
  const status = tokenStore.getWalletStatus();
}

// 现在
if (accountStore.isWalletConnected) {
  const status = accountStore.getWalletStatus();
}
```

### 3. Token操作保持不变

```typescript
// 这些操作保持不变
await tokenStore.getTokenList(chainId, destChainId);
await tokenStore.fetchCurrentWalletBalances();
await tokenStore.refreshBalances();
```

## 测试结果

- ✅ TypeScript 编译通过
- ✅ 所有接口保持向后兼容
- ✅ 依赖注入正常工作
- ✅ 示例代码运行正常

## 总结

这次重构成功实现了：

1. **职责分离**: 账户管理和Token管理分离
2. **依赖注入**: TokenStore 通过依赖注入使用 AccountStore
3. **向后兼容**: 保持了现有API的兼容性
4. **代码质量**: 提高了代码的可维护性和可测试性
5. **架构清晰**: 每个Store都有明确的职责边界

这个重构为后续的功能扩展和维护奠定了良好的基础，是现代前端应用状态管理的最佳实践。
