# Store 架构重构指南

本指南介绍了将账户管理功能从 `TokenStore` 分离到独立的 `AccountStore` 的架构重构。

## 架构概述

### 重构前
```
TokenStore
├── Token 管理
├── 余额获取
├── 钱包连接检测  ❌ 职责混乱
├── 地址获取      ❌ 职责混乱
└── 钱包状态管理  ❌ 职责混乱
```

### 重构后
```
AccountStore (新增)
├── 钱包连接检测
├── 地址获取
├── 钱包状态管理
├── 链ID管理
└── 连接器管理

TokenStore (重构)
├── Token 管理
├── 余额获取
└── 依赖 AccountStore ✅ 职责清晰
```

## 核心组件

### 1. AccountStore

专门负责账户和钱包相关的所有功能：

```typescript
export class AccountStore {
  // 核心状态
  get walletInfo(): WalletInfo;
  get isWalletConnected(): boolean;
  get walletAddress(): string | undefined;
  get currentChainId(): number | undefined;

  // 状态查询
  getWalletStatus(): WalletStatus;
  getConnectionSummary(): object;
  
  // 工具方法
  ensureWalletConnected(): string;
  isOnChain(chainId: number): boolean;
  getShortAddress(address?: string): string | undefined;
  isCurrentWalletAddress(address: string): boolean;
}
```

### 2. TokenStore (重构后)

专注于 Token 管理，通过依赖注入使用 AccountStore：

```typescript
export class TokenStore {
  private accountStore?: AccountStore;

  constructor(accountStore?: AccountStore);
  setAccountStore(accountStore: AccountStore): void;

  // 委托给 AccountStore 的属性
  get isWalletConnected(): boolean;
  get walletAddress(): string | undefined;

  // Token 相关功能
  async getTokenList(chainId: number, destChainId: number);
  async batchFetchTokenBalance(tokens: Token[], address?: string);
  async fetchCurrentWalletBalances();
}
```

## 使用方法

### 1. 基础用法

```typescript
import { useStore } from '@/store';

const MyComponent = observer(() => {
  const { accountStore, tokenStore } = useStore();

  // 使用 AccountStore 检查钱包状态
  if (!accountStore.isWalletConnected) {
    return <div>请连接钱包</div>;
  }

  // 使用 TokenStore 管理Token
  const fetchBalances = async () => {
    await tokenStore.fetchCurrentWalletBalances();
  };

  return (
    <div>
      <p>地址: {accountStore.walletAddress}</p>
      <p>Token数量: {tokenStore.tokenList.length}</p>
      <button onClick={fetchBalances}>获取余额</button>
    </div>
  );
});
```

### 2. 钱包状态管理

```typescript
const WalletStatus = observer(() => {
  const { accountStore } = useStore();

  const status = accountStore.getWalletStatus();

  return (
    <div>
      <h3>钱包状态</h3>
      <p>连接状态: {status.isConnected ? '已连接' : '未连接'}</p>
      <p>地址: {status.addressShort}</p>
      <p>链ID: {status.chainId}</p>
      <p>连接器: {status.connectorName}</p>
      <p>钱包类型: {accountStore.getWalletTypeDescription()}</p>
    </div>
  );
});
```

### 3. Token 余额管理

```typescript
const TokenBalances = observer(() => {
  const { accountStore, tokenStore } = useStore();

  const fetchBalances = async () => {
    try {
      // AccountStore 确保钱包连接
      accountStore.ensureWalletConnected();
      
      // TokenStore 获取余额
      await tokenStore.fetchCurrentWalletBalances();
    } catch (error) {
      console.error('获取余额失败:', error);
    }
  };

  return (
    <div>
      {tokenStore.tokenList.map(token => (
        <div key={token.id}>
          {token.symbol}: {token.balance.amount}
        </div>
      ))}
      <button onClick={fetchBalances}>刷新余额</button>
    </div>
  );
});
```

### 4. 响应式状态监听

```typescript
const AutoBalanceUpdater = observer(() => {
  const { accountStore, tokenStore } = useStore();

  useEffect(() => {
    // 监听钱包连接状态和Token列表变化
    if (accountStore.isWalletConnected && tokenStore.tokenList.length > 0) {
      tokenStore.fetchCurrentWalletBalances();
    }
  }, [accountStore.isWalletConnected, tokenStore.tokenList.length]);

  return null; // 这是一个后台组件
});
```

## 依赖注入

### RootStore 中的配置

```typescript
export class RootStore {
  get tokenStore(): TokenStore {
    if (!this._tokenStore) {
      this._tokenStore = new TokenStore();
      // 设置 AccountStore 依赖
      this._tokenStore.setAccountStore(this.accountStore);
    }
    return this._tokenStore;
  }

  get accountStore(): AccountStore {
    if (!this._accountStore) {
      this._accountStore = new AccountStore();
    }
    return this._accountStore;
  }
}
```

### 延迟依赖注入

```typescript
// TokenStore 中的实现
private getAccountStore(): AccountStore {
  if (!this.accountStore) {
    throw new Error('AccountStore not initialized. Please call setAccountStore() first.');
  }
  return this.accountStore;
}

// 使用时自动检查依赖
async batchFetchTokenBalance(tokens: Token[], address?: string) {
  const targetAddress = address || this.getAccountStore().ensureWalletConnected();
  // ...
}
```

## 架构优势

### 1. 单一职责原则
- **AccountStore**: 只负责账户和钱包管理
- **TokenStore**: 只负责Token和余额管理

### 2. 依赖倒置
- TokenStore 依赖 AccountStore 的抽象接口
- 便于测试和模拟

### 3. 代码复用
- AccountStore 可以被其他Store复用
- 避免重复的钱包管理代码

### 4. 易于测试
```typescript
// 可以独立测试 AccountStore
const accountStore = new AccountStore();
expect(accountStore.isWalletConnected).toBe(false);

// 可以模拟 AccountStore 来测试 TokenStore
const mockAccountStore = {
  ensureWalletConnected: () => '0x123...',
  isWalletConnected: true,
  walletAddress: '0x123...'
};
const tokenStore = new TokenStore();
tokenStore.setAccountStore(mockAccountStore);
```

### 5. 易于扩展
- 新增钱包功能只需修改 AccountStore
- 新增Token功能只需修改 TokenStore
- 互不影响

## 迁移指南

### 1. 更新导入

```typescript
// 之前
const { tokenStore } = useStore();
const isConnected = tokenStore.isWalletConnected;

// 现在
const { accountStore, tokenStore } = useStore();
const isConnected = accountStore.isWalletConnected;
```

### 2. 更新钱包状态检查

```typescript
// 之前
if (tokenStore.isWalletConnected) {
  const status = tokenStore.getWalletStatus();
}

// 现在
if (accountStore.isWalletConnected) {
  const status = accountStore.getWalletStatus();
}
```

### 3. 更新余额获取

```typescript
// 之前
await tokenStore.fetchCurrentWalletBalances();

// 现在（保持不变）
await tokenStore.fetchCurrentWalletBalances();
// TokenStore 内部会自动使用 AccountStore
```

## 最佳实践

### 1. 组件设计
```typescript
// 推荐：明确区分职责
const WalletInfo = observer(() => {
  const { accountStore } = useStore();
  return <div>{accountStore.walletAddress}</div>;
});

const TokenList = observer(() => {
  const { tokenStore } = useStore();
  return <div>{tokenStore.tokenList.length} tokens</div>;
});
```

### 2. 错误处理
```typescript
try {
  accountStore.ensureWalletConnected();
  await tokenStore.fetchCurrentWalletBalances();
} catch (error) {
  if (error.message.includes('钱包未连接')) {
    // 处理钱包连接错误
  } else {
    // 处理其他错误
  }
}
```

### 3. 状态监听
```typescript
// 使用 reaction 监听状态变化
import { reaction } from 'mobx';

const disposer = reaction(
  () => accountStore.isWalletConnected,
  (isConnected) => {
    if (isConnected && tokenStore.tokenList.length > 0) {
      tokenStore.fetchCurrentWalletBalances();
    }
  }
);
```

这个架构重构提供了更清晰的职责分离、更好的代码组织和更强的可维护性，是现代前端应用状态管理的最佳实践。
