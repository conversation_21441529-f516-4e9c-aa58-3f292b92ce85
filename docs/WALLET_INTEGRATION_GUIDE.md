# TokenStore 钱包集成指南

本指南介绍如何在 `TokenStore` 中判断钱包连接状态和获取钱包地址，以及相关的最佳实践。

## 功能概述

`TokenStore` 现在提供了完整的钱包状态管理功能：

1. **钱包连接检测**: 实时检测钱包连接状态
2. **地址获取**: 自动获取当前连接的钱包地址
3. **智能余额获取**: 支持自动使用当前钱包地址
4. **状态管理**: 提供详细的钱包状态信息
5. **错误处理**: 完善的错误处理和用户提示

## 核心 API

### 1. 钱包状态属性

```typescript
// 获取完整的钱包信息
const walletInfo = tokenStore.walletInfo;
// {
//   isConnected: boolean,
//   address: string | undefined,
//   chainId: number | undefined,
//   connector: Connector | undefined
// }

// 检查钱包是否已连接
const isConnected = tokenStore.isWalletConnected;

// 获取钱包地址
const address = tokenStore.walletAddress;

// 获取当前链ID
const chainId = tokenStore.currentChainId;
```

### 2. 增强的余额获取方法

```typescript
// 方法重载：支持自动使用当前钱包地址
await tokenStore.batchFetchTokenBalance(tokens); // 使用当前钱包地址
await tokenStore.batchFetchTokenBalance(tokens, address); // 使用指定地址

// 便捷方法
await tokenStore.fetchCurrentWalletBalances(); // 获取当前钱包的所有Token余额
await tokenStore.refreshBalances(); // 刷新余额
await tokenStore.ensureWalletAndFetchBalances(); // 检查钱包并获取余额
```

### 3. 钱包状态查询

```typescript
// 获取详细的钱包状态
const status = tokenStore.getWalletStatus();
// {
//   isConnected: boolean,
//   address: string | undefined,
//   chainId: number | undefined,
//   connector: Connector | undefined,
//   addressShort: string | undefined, // 缩短的地址显示
//   connectorName: string | undefined // 连接器名称
// }
```

## 使用方法

### 1. 基础钱包检测

```typescript
import { observer } from 'mobx-react-lite';
import { useStore } from '@/store';

const WalletComponent = observer(() => {
  const { tokenStore } = useStore();

  // 检查钱包连接状态
  if (!tokenStore.isWalletConnected) {
    return <div>请连接钱包</div>;
  }

  return (
    <div>
      <p>钱包地址: {tokenStore.walletAddress}</p>
      <p>链ID: {tokenStore.currentChainId}</p>
    </div>
  );
});
```

### 2. 自动余额获取

```typescript
const TokenList = observer(() => {
  const { tokenStore } = useStore();

  const fetchBalances = async () => {
    try {
      if (!tokenStore.isWalletConnected) {
        alert('请先连接钱包');
        return;
      }

      // 自动使用当前钱包地址
      await tokenStore.fetchCurrentWalletBalances();
    } catch (error) {
      console.error('获取余额失败:', error);
    }
  };

  return (
    <div>
      <button onClick={fetchBalances}>获取余额</button>
      {tokenStore.tokenList.map(token => (
        <div key={token.id}>
          {token.symbol}: {token.balance.amount}
        </div>
      ))}
    </div>
  );
});
```

### 3. 响应式钱包状态

```typescript
import { useEffect } from 'react';

const AutoBalanceComponent = observer(() => {
  const { tokenStore } = useStore();

  // 当钱包连接状态或Token列表变化时自动获取余额
  useEffect(() => {
    if (tokenStore.isWalletConnected && tokenStore.tokenList.length > 0) {
      tokenStore.fetchCurrentWalletBalances().catch(console.error);
    }
  }, [tokenStore.isWalletConnected, tokenStore.tokenList.length]);

  return (
    <div>
      {tokenStore.isWalletConnected ? (
        <div>
          <p>钱包已连接: {tokenStore.walletAddress}</p>
          <p>Token数量: {tokenStore.tokenList.length}</p>
        </div>
      ) : (
        <div>请连接钱包</div>
      )}
    </div>
  );
});
```

### 4. 完整的工作流程

```typescript
const CompleteWorkflow = observer(() => {
  const { tokenStore } = useStore();
  const [loading, setLoading] = useState(false);

  const handleCompleteFlow = async () => {
    setLoading(true);
    
    try {
      // 1. 检查钱包连接
      if (!tokenStore.isWalletConnected) {
        throw new Error('请先连接钱包');
      }

      // 2. 获取Token列表
      await tokenStore.getTokenList(4689, 1);

      // 3. 获取余额（自动使用当前钱包地址）
      await tokenStore.fetchCurrentWalletBalances();

      console.log('完整流程执行成功');
    } catch (error) {
      console.error('流程执行失败:', error);
      alert(error.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <button onClick={handleCompleteFlow} disabled={loading}>
        {loading ? '处理中...' : '执行完整流程'}
      </button>
    </div>
  );
});
```

## 错误处理

### 1. 钱包未连接

```typescript
try {
  await tokenStore.fetchCurrentWalletBalances();
} catch (error) {
  if (error.message === '钱包未连接，无法获取余额') {
    // 提示用户连接钱包
    showConnectWalletModal();
  }
}
```

### 2. Token列表为空

```typescript
try {
  await tokenStore.fetchCurrentWalletBalances();
} catch (error) {
  if (error.message === 'Token列表为空，请先获取Token列表') {
    // 自动获取Token列表
    await tokenStore.getTokenList(defaultChainId, defaultDestChainId);
    await tokenStore.fetchCurrentWalletBalances();
  }
}
```

### 3. 使用 ensureWalletAndFetchBalances

```typescript
// 这个方法会自动处理常见的错误情况
try {
  await tokenStore.ensureWalletAndFetchBalances();
} catch (error) {
  if (error.message === '请先连接钱包') {
    showConnectWalletModal();
  } else {
    showErrorMessage(error.message);
  }
}
```

## 最佳实践

### 1. 响应式状态监听

```typescript
import { reaction } from 'mobx';

// 监听钱包连接状态变化
const disposer = reaction(
  () => tokenStore.isWalletConnected,
  (isConnected) => {
    if (isConnected && tokenStore.tokenList.length > 0) {
      tokenStore.fetchCurrentWalletBalances();
    }
  }
);

// 组件卸载时清理
useEffect(() => {
  return () => disposer();
}, []);
```

### 2. 钱包状态显示组件

```typescript
const WalletStatus = observer(() => {
  const { tokenStore } = useStore();
  const status = tokenStore.getWalletStatus();

  return (
    <div className="wallet-status">
      <div className={`status-indicator ${status.isConnected ? 'connected' : 'disconnected'}`}>
        {status.isConnected ? '已连接' : '未连接'}
      </div>
      
      {status.isConnected && (
        <div className="wallet-info">
          <p>地址: {status.addressShort}</p>
          <p>链: {status.chainId}</p>
          <p>连接器: {status.connectorName}</p>
        </div>
      )}
    </div>
  );
});
```

### 3. 自动刷新余额

```typescript
const useAutoRefreshBalances = (interval = 30000) => {
  const { tokenStore } = useStore();

  useEffect(() => {
    if (!tokenStore.isWalletConnected) return;

    const timer = setInterval(() => {
      if (tokenStore.tokenList.length > 0) {
        tokenStore.refreshBalances().catch(console.error);
      }
    }, interval);

    return () => clearInterval(timer);
  }, [tokenStore.isWalletConnected, interval]);
};
```

### 4. 条件渲染

```typescript
const ConditionalContent = observer(() => {
  const { tokenStore } = useStore();

  if (!tokenStore.isWalletConnected) {
    return <ConnectWalletPrompt />;
  }

  if (tokenStore.tokenList.length === 0) {
    return <LoadTokenListPrompt />;
  }

  return <TokenListWithBalances />;
});
```

## 技术实现

### 1. 使用 wagmi 的 getAccount

```typescript
import { getAccount } from '@wagmi/core';

get walletInfo() {
  const account = getAccount(wagmiAdapter.wagmiConfig);
  return {
    isConnected: account.isConnected,
    address: account.address,
    chainId: account.chainId,
    connector: account.connector,
  };
}
```

### 2. 计算属性

```typescript
get isWalletConnected(): boolean {
  return this.walletInfo.isConnected;
}

get walletAddress(): string | undefined {
  return this.walletInfo.address;
}
```

### 3. 方法重载

```typescript
async batchFetchTokenBalance(tokens: Token[]): Promise<(bigint | undefined)[]>;
async batchFetchTokenBalance(tokens: Token[], address: string): Promise<(bigint | undefined)[]>;
async batchFetchTokenBalance(tokens: Token[], address?: string): Promise<(bigint | undefined)[]> {
  const targetAddress = address || this.walletAddress;
  if (!targetAddress) {
    throw new Error('钱包未连接，无法获取余额');
  }
  // ... 实现
}
```

## 注意事项

1. **实时性**: 钱包状态是实时获取的，确保数据准确性
2. **错误处理**: 始终处理钱包未连接的情况
3. **性能**: 避免频繁调用钱包状态检查
4. **用户体验**: 提供清晰的状态提示和错误信息
5. **类型安全**: 利用 TypeScript 的类型检查

这个钱包集成系统提供了完整的钱包状态管理功能，让 Token 余额的获取和管理变得更加智能和用户友好。
