# Token 余额管理指南

本指南介绍如何使用增强的 `TokenStore.batchFetchTokenBalance` 方法来获取和管理 Token 余额。

## 功能概述

新的 `batchFetchTokenBalance` 方法现在会：

1. **自动设置余额到Token对象**: 余额会直接设置到每个Token的 `balance` 属性中
2. **支持加载状态**: 在获取余额时显示加载状态
3. **错误处理**: 获取失败时设置余额为0并记录错误
4. **自动格式化**: 根据Token的小数位数自动格式化余额显示

## 核心特性

### 1. 加载状态管理

```typescript
// 获取余额前，所有Token的balance.loading会设置为true
token.balance.loading; // true - 正在加载
token.balance.amount;  // "..." - 加载时显示

// 获取完成后，loading设置为false
token.balance.loading; // false - 加载完成
token.balance.amount;  // "1,234.56" - 格式化的余额
```

### 2. 自动小数位数处理

```typescript
// 如果Token有decimals属性，会自动设置到balance中
if (token.decimals !== undefined) {
  token.balance.setDecimal(token.decimals);
}

// 余额会根据正确的小数位数格式化
token.balance.rawAmount;      // "1234560000000000000000" (原始值)
token.balance.formattedAmount; // "1,234.56" (格式化值)
```

### 3. 错误处理

```typescript
// 获取成功
if (result.status === 'success' && result.result) {
  token.setBalance(result.result as bigint);
} else {
  // 获取失败，设置为0
  token.setBalance(0n);
  console.warn(`Failed to fetch balance for token ${token.symbol}`);
}
```

## 使用方法

### 1. 基础用法

```typescript
import { useStore } from '@/store';
import { useAppKitAccount } from '@reown/appkit/react';

function MyComponent() {
  const { tokenStore } = useStore();
  const { address } = useAppKitAccount();

  const fetchBalances = async () => {
    if (!address) return;
    
    try {
      await tokenStore.batchFetchTokenBalance(tokenStore.tokenList, address);
      // 余额已自动设置到每个Token的balance属性中
    } catch (error) {
      console.error('获取余额失败:', error);
    }
  };

  return (
    <div>
      {tokenStore.tokenList.map(token => (
        <div key={token.id}>
          <span>{token.symbol}: {token.balance.amount}</span>
          {token.balance.loading && <span>加载中...</span>}
        </div>
      ))}
      <button onClick={fetchBalances}>获取余额</button>
    </div>
  );
}
```

### 2. 使用 MobX Observer

```typescript
import { observer } from 'mobx-react-lite';

const TokenList = observer(() => {
  const { tokenStore } = useStore();

  return (
    <div>
      {tokenStore.tokenList.map(token => (
        <TokenCard key={token.id} token={token} />
      ))}
    </div>
  );
});

const TokenCard = observer(({ token }: { token: Token }) => {
  return (
    <div className="token-card">
      <h3>{token.symbol}</h3>
      <p>余额: {token.balance.amount}</p>
      <p>原始值: {token.balance.rawAmount}</p>
      {token.balance.loading && <p>加载中...</p>}
    </div>
  );
});
```

### 3. 自动获取余额

```typescript
import { useEffect } from 'react';

const AutoBalanceComponent = observer(() => {
  const { tokenStore } = useStore();
  const { address } = useAppKitAccount();

  // 当地址或token列表变化时自动获取余额
  useEffect(() => {
    if (address && tokenStore.tokenList.length > 0) {
      tokenStore.batchFetchTokenBalance(tokenStore.tokenList, address);
    }
  }, [address, tokenStore.tokenList.length]);

  return (
    <div>
      {/* 组件内容 */}
    </div>
  );
});
```

### 4. 自定义Hook

```typescript
export const useTokenBalances = () => {
  const { tokenStore } = useStore();
  const { address } = useAppKitAccount();

  const fetchBalances = async (tokens?: Token[]) => {
    if (!address) throw new Error('钱包未连接');
    
    const tokensToFetch = tokens || tokenStore.tokenList;
    return await tokenStore.batchFetchTokenBalance(tokensToFetch, address);
  };

  const getTokenBalance = (tokenAddress: string) => {
    const token = tokenStore.tokenList.find(t => t.address === tokenAddress);
    return token?.balance;
  };

  return {
    fetchBalances,
    getTokenBalance,
    isConnected: !!address,
    address,
  };
};
```

## Token Balance 属性

每个Token的 `balance` 属性是 `BigIntState` 类型，提供以下功能：

### 属性

- `balance.value: bigint` - 原始余额值
- `balance.decimal: number` - 小数位数
- `balance.loading: boolean` - 加载状态

### 方法

- `balance.setValue(value: bigint)` - 设置余额值
- `balance.setDecimal(decimal: number)` - 设置小数位数
- `balance.setLoading(loading: boolean)` - 设置加载状态

### 计算属性

- `balance.amount: string` - 格式化的余额（带千分位分隔符）
- `balance.rawAmount: string` - 原始余额字符串
- `balance.formattedAmount: string` - 格式化的余额

## 最佳实践

### 1. 错误处理

```typescript
const fetchBalances = async () => {
  try {
    await tokenStore.batchFetchTokenBalance(tokens, address);
  } catch (error) {
    // 处理网络错误或其他异常
    console.error('获取余额失败:', error);
    // 显示用户友好的错误信息
    showErrorMessage('无法获取余额，请检查网络连接');
  }
};
```

### 2. 加载状态显示

```typescript
const TokenBalance = observer(({ token }: { token: Token }) => {
  if (token.balance.loading) {
    return <div className="loading">获取余额中...</div>;
  }

  return (
    <div>
      {token.balance.amount} {token.symbol}
    </div>
  );
});
```

### 3. 定期刷新

```typescript
useEffect(() => {
  const interval = setInterval(() => {
    if (address && tokenStore.tokenList.length > 0) {
      tokenStore.batchFetchTokenBalance(tokenStore.tokenList, address);
    }
  }, 30000); // 每30秒刷新一次

  return () => clearInterval(interval);
}, [address, tokenStore.tokenList.length]);
```

## 注意事项

1. **网络错误**: 确保处理网络连接失败的情况
2. **钱包连接**: 在调用前检查钱包是否已连接
3. **Token地址**: 确保Token地址格式正确（0x开头的42位十六进制字符串）
4. **链ID**: 确保Token的chainId与当前网络匹配
5. **性能**: 避免频繁调用，建议使用防抖或节流

这个增强的余额管理系统提供了完整的状态管理、错误处理和用户体验优化，让Token余额的获取和显示变得更加简单和可靠。
